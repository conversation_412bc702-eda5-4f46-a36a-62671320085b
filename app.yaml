kind: Service
apiVersion: v1
metadata:
  name: mico-epjm
  labels:
    app: mico-epjm
spec:
  type: ClusterIP
  selector:
    app: mico-epjm
  ports:
    - name: tcp
      port: 8080
      protocol: TCP
      targetPort: 8080
---
kind: Deployment
apiVersion: apps/v1
metadata:
  name: mico-epjm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mico-epjm
  template:
    metadata:
      labels:
        app: mico-epjm
        version: v1
    spec:
      containers:
      - name: mico-epjm
        image: mico-epjm
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8080
