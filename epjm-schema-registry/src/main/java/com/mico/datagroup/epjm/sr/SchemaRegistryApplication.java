package com.mico.datagroup.epjm.sr;

import io.helidon.logging.common.LogConfig;
import io.helidon.webserver.WebServer;
import io.helidon.webserver.http.HttpRouting;

/**
 * The Schema Registry application main class.
 */
public class SchemaRegistryApplication {

    /**
     * Cannot be instantiated.
     */
    private SchemaRegistryApplication() {
    }

    /**
     * Application main entry point.
     *
     * @param args command line arguments.
     */
    public static void main(String[] args) {

        // load logging configuration
        LogConfig.configureRuntime();

        WebServer server = WebServer.builder()
                .routing(SchemaRegistryApplication::routing)
                .port(8080)
                .build()
                .start();

        System.out.println("Schema Registry server is up! http://localhost:" + server.port() + "/schemas");
    }

    /**
     * Updates HTTP Routing for Schema Registry.
     */
    static void routing(HttpRouting.Builder routing) {
        routing
                .get("/health", (req, res) -> res.send("Schema Registry is healthy!"));
    }
}
