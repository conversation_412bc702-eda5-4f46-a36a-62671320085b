<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.mico.datagroup</groupId>
    <artifactId>mico-epjm</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>MICO EPJM Parent</name>
    <description>Parent project for EPJM Schema Registry and Job Manager modules</description>

    <modules>
        <module>epjm-schema-registry</module>
        <module>epjm-job-manager</module>
    </modules>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <helidon.version>4.2.6</helidon.version>
        <junit.version>5.10.0</junit.version>
        <hamcrest.version>2.2</hamcrest.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Helidon BOM -->
            <dependency>
                <groupId>io.helidon.applications</groupId>
                <artifactId>helidon-se</artifactId>
                <version>${helidon.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Common dependencies with managed versions -->
            <dependency>
                <groupId>io.helidon.webserver</groupId>
                <artifactId>helidon-webserver</artifactId>
                <version>${helidon.version}</version>
            </dependency>
            <dependency>
                <groupId>io.helidon.config</groupId>
                <artifactId>helidon-config-yaml</artifactId>
                <version>${helidon.version}</version>
            </dependency>
            <dependency>
                <groupId>io.helidon.webserver.observe</groupId>
                <artifactId>helidon-webserver-observe-health</artifactId>
                <version>${helidon.version}</version>
            </dependency>
            <dependency>
                <groupId>io.helidon.health</groupId>
                <artifactId>helidon-health-checks</artifactId>
                <version>${helidon.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.15.2</version>
            </dependency>
            <dependency>
                <groupId>io.helidon.http.media</groupId>
                <artifactId>helidon-http-media-jackson</artifactId>
                <version>${helidon.version}</version>
            </dependency>
            <dependency>
                <groupId>io.helidon.logging</groupId>
                <artifactId>helidon-logging-jul</artifactId>
                <version>${helidon.version}</version>
            </dependency>
            <dependency>
                <groupId>io.helidon.webserver.observe</groupId>
                <artifactId>helidon-webserver-observe-metrics</artifactId>
                <version>${helidon.version}</version>
            </dependency>
            <dependency>
                <groupId>io.helidon.metrics</groupId>
                <artifactId>helidon-metrics-system-meters</artifactId>
                <version>${helidon.version}</version>
            </dependency>
            <dependency>
                <groupId>io.helidon.webclient</groupId>
                <artifactId>helidon-webclient</artifactId>
                <version>${helidon.version}</version>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-api</artifactId>
                <version>${junit.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hamcrest</groupId>
                <artifactId>hamcrest-all</artifactId>
                <version>1.3</version>
            </dependency>
            <dependency>
                <groupId>io.helidon.webserver.testing.junit5</groupId>
                <artifactId>helidon-webserver-testing-junit5</artifactId>
                <version>${helidon.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.11.0</version>
                    <configuration>
                        <source>21</source>
                        <target>21</target>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>3.6.0</version>
                    <executions>
                        <execution>
                            <id>copy-libs</id>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.1.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <version>3.1.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>3.5.0</version>
                    <executions>
                        <execution>
                            <phase>package</phase>
                            <goals>
                                <goal>shade</goal>
                            </goals>
                            <configuration>
                                <createDependencyReducedPom>false</createDependencyReducedPom>
                                <transformers>
                                    <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                        <mainClass>${mainClass}</mainClass>
                                    </transformer>
                                    <transformer implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer"/>
                                </transformers>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
