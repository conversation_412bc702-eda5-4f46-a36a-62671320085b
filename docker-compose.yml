version: '3.8'

services:
  schema-registry:
    build:
      context: .
      dockerfile: docker/schema-registry/Dockerfile
    ports:
      - "8081:8080"
    environment:
      - SERVER_PORT=8080
    networks:
      - epjm-network

  job-manager:
    build:
      context: .
      dockerfile: docker/job-manager/Dockerfile
    ports:
      - "8082:8080"
    environment:
      - SERVER_PORT=8080
    networks:
      - epjm-network
    depends_on:
      - schema-registry

networks:
  epjm-network:
    driver: bridge
