# 1st stage, build the app
FROM ghcr.io/graalvm/graalvm-community:21 as build

WORKDIR /usr/share

# Install maven
RUN set -x && \
    curl -O https://archive.apache.org/dist/maven/maven-3/3.8.4/binaries/apache-maven-3.8.4-bin.tar.gz && \
    tar -xvf apache-maven-*-bin.tar.gz  && \
    rm apache-maven-*-bin.tar.gz && \
    mv apache-maven-* maven && \
    ln -s /usr/share/maven/bin/mvn /bin/

WORKDIR /helidon

# Create a first layer to cache the "Maven World" in the local repository.
# Incremental docker builds will always resume after that, unless you update
# the pom
ADD pom.xml .
ADD epjm-schema-registry/pom.xml epjm-schema-registry/
ADD epjm-job-manager/pom.xml epjm-job-manager/
RUN mvn package -Pnative-image -Dnative.image.skip -Dmaven.test.skip -Declipselink.weave.skip -pl epjm-schema-registry

# Do the Maven build!
# Incremental docker builds will resume here when you change sources
ADD epjm-schema-registry/src epjm-schema-registry/src
RUN mvn package -Pnative-image -Dnative.image.buildStatic -DskipTests -pl epjm-schema-registry

RUN echo "done!"

# 2nd stage, build the runtime image
FROM scratch
WORKDIR /helidon

# Copy the binary built in the 1st stage
COPY --from=build /helidon/epjm-schema-registry/target/epjm-schema-registry ./

ENTRYPOINT ["/helidon/epjm-schema-registry"]

EXPOSE 8080
