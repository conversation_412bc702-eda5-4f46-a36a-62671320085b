# 1st stage, build the app
FROM eclipse-temurin:21-jdk as build

# Install maven
WORKDIR /usr/share
RUN set -x && \
    curl -O https://archive.apache.org/dist/maven/maven-3/3.8.4/binaries/apache-maven-3.8.4-bin.tar.gz && \
    tar -xvf apache-maven-*-bin.tar.gz  && \
    rm apache-maven-*-bin.tar.gz && \
    mv apache-maven-* maven && \
    ln -s /usr/share/maven/bin/mvn /bin/

WORKDIR /helidon

# Create a first layer to cache the "Maven World" in the local repository.
# Incremental docker builds will always resume after that, unless you update
# the pom
ADD pom.xml .
ADD epjm-schema-registry/pom.xml epjm-schema-registry/
ADD epjm-job-manager/pom.xml epjm-job-manager/
RUN mvn package -Dmaven.test.skip -Declipselink.weave.skip -pl epjm-job-manager

# Do the Maven build!
# Incremental docker builds will resume here when you change sources
ADD epjm-job-manager/src epjm-job-manager/src
RUN mvn package -DskipTests -pl epjm-job-manager

RUN echo "done!"

# 2nd stage, build the runtime image
FROM eclipse-temurin:21-jre
WORKDIR /helidon

# Copy the binary built in the 1st stage
COPY --from=build /helidon/epjm-job-manager/target/epjm-job-manager-1.0-SNAPSHOT.jar ./epjm-job-manager.jar

CMD ["java", "-jar", "epjm-job-manager.jar"]

EXPOSE 8080
