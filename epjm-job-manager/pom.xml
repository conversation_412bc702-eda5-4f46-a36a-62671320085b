<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.mico.datagroup</groupId>
        <artifactId>mico-epjm</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>epjm-job-manager</artifactId>
    <name>EPJM Job Manager</name>
    <description>Job Manager service for EPJM</description>

    <properties>
        <mainClass>com.mico.datagroup.epjm.jm.JobManagerApplication</mainClass>
    </properties>

    <dependencies>
        <!-- Helidon SE dependencies -->
        <dependency>
            <groupId>io.helidon.webserver</groupId>
            <artifactId>helidon-webserver</artifactId>
        </dependency>
        <dependency>
            <groupId>io.helidon.config</groupId>
            <artifactId>helidon-config-yaml</artifactId>
        </dependency>
        <dependency>
            <groupId>io.helidon.webserver.observe</groupId>
            <artifactId>helidon-webserver-observe-health</artifactId>
        </dependency>
        <dependency>
            <groupId>io.helidon.health</groupId>
            <artifactId>helidon-health-checks</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>io.helidon.http.media</groupId>
            <artifactId>helidon-http-media-jackson</artifactId>
        </dependency>
        <dependency>
            <groupId>io.helidon.logging</groupId>
            <artifactId>helidon-logging-jul</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.helidon.webserver.observe</groupId>
            <artifactId>helidon-webserver-observe-metrics</artifactId>
        </dependency>
        <dependency>
            <groupId>io.helidon.metrics</groupId>
            <artifactId>helidon-metrics-system-meters</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- Test dependencies -->
        <dependency>
            <groupId>io.helidon.webclient</groupId>
            <artifactId>helidon-webclient</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-all</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.helidon.webserver.testing.junit5</groupId>
            <artifactId>helidon-webserver-testing-junit5</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-libs</id>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
