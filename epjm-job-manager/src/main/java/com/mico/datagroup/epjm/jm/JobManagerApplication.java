package com.mico.datagroup.epjm.jm;

import io.helidon.logging.common.LogConfig;
import io.helidon.config.Config;
import io.helidon.webserver.WebServer;
import io.helidon.webserver.http.HttpRouting;

/**
 * The Job Manager application main class.
 */
public class JobManagerApplication {

    /**
     * Cannot be instantiated.
     */
    private JobManagerApplication() {
    }

    /**
     * Application main entry point.
     *
     * @param args command line arguments.
     */
    public static void main(String[] args) {

        // load logging configuration
        LogConfig.configureRuntime();

        // initialize config from default configuration
        Config config = Config.global();

        WebServer server = WebServer.builder()
                .config(config.get("server"))
                .routing(JobManagerApplication::routing)
                .build()
                .start();

        System.out.println("Job Manager server is up! http://localhost:" + server.port() + "/jobs");
    }

    /**
     * Updates HTTP Routing for Job Manager.
     */
    static void routing(HttpRouting.Builder routing) {
        routing.get("/health", (req, res) -> res.send("Job Manager is healthy!"));
    }
}
