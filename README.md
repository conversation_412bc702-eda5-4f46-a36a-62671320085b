# mico-epjm

Multi-module Helidon SE project with Schema Registry and Job Manager services.

## Project Structure

This project consists of two main modules:
- **epjm-schema-registry**: Schema Registry service for managing data schemas
- **epjm-job-manager**: Job Manager service for managing and executing jobs

## Build and run

### Build all modules
With JDK21
```bash
mvn clean package
```

### Run Schema Registry
```bash
java -jar epjm-schema-registry/target/epjm-schema-registry.jar
```

### Run Job Manager
```bash
java -jar epjm-job-manager/target/epjm-job-manager.jar
```

### Build individual modules
```bash
# Build only Schema Registry
mvn clean package -pl epjm-schema-registry

# Build only Job Manager
mvn clean package -pl epjm-job-manager
```

## Docker Support

Dockerfiles are organized in the `docker/` directory with subdirectories for each module. All builds use OpenJDK and Maven Shade Plugin for fat JARs.

### Standard Docker Build
```bash
# Build Schema Registry (fat JAR with <PERSON><PERSON> Plugin)
docker build -f docker/schema-registry/Dockerfile -t epjm-schema-registry .

# Build Job Manager (fat JAR with Maven Shade Plugin)
docker build -f docker/job-manager/Dockerfile -t epjm-job-manager .
```

### JLink Custom Runtime Image
```bash
# Build with JLink (smaller runtime)
docker build -f docker/schema-registry/Dockerfile.jlink -t epjm-schema-registry:jlink .
docker build -f docker/job-manager/Dockerfile.jlink -t epjm-job-manager:jlink .
```

### Native Image (GraalVM)
```bash
# Build native image (fastest startup)
docker build -f docker/schema-registry/Dockerfile.native -t epjm-schema-registry:native .
docker build -f docker/job-manager/Dockerfile.native -t epjm-job-manager:native .
```

### Docker Compose
Run both services together:
```bash
docker-compose up
```

This will start:
- Schema Registry on http://localhost:8081
- Job Manager on http://localhost:8082

## Exercise the applications

### Schema Registry Service (default port 8080)

Health check:
```bash
curl -X GET http://localhost:8080/health
```

Schema operations:
```bash
# Get all schemas
curl -X GET http://localhost:8080/schemas

# Get specific schema
curl -X GET http://localhost:8080/schemas/default

# Create new schema
curl -X POST http://localhost:8080/schemas

# Delete schema
curl -X DELETE http://localhost:8080/schemas/default
```

### Job Manager Service (default port 8080)

Health check:
```bash
curl -X GET http://localhost:8080/health
```

Job operations:
```bash
# Get all jobs
curl -X GET http://localhost:8080/jobs

# Get specific job
curl -X GET http://localhost:8080/jobs/{jobId}

# Create new job
curl -X POST http://localhost:8080/jobs

# Start job
curl -X POST http://localhost:8080/jobs/{jobId}/start

# Stop job
curl -X POST http://localhost:8080/jobs/{jobId}/stop
```

Legacy greeting endpoints (Job Manager):
```bash
curl -X GET http://localhost:8080/simple-greet
curl -X GET http://localhost:8080/greet
curl -X GET http://localhost:8080/greet/Joe
curl -X PUT -H "Content-Type: application/json" -d '{"greeting" : "Hola"}' http://localhost:8080/greet/greeting
```



## Try metrics

```
# Prometheus Format
curl -s -X GET http://localhost:8080/observe/metrics
# TYPE base:gc_g1_young_generation_count gauge
. . .

# JSON Format
curl -H 'Accept: application/json' -X GET http://localhost:8080/observe/metrics
{"base":...
. . .
```


## Try health

This example shows the basics of using Helidon SE Health. It uses the
set of built-in health checks that Helidon provides plus defines a
custom health check.

Note the port number reported by the application.

Probe the health endpoints:

```bash
curl -X GET http://localhost:8080/observe/health
curl -X GET http://localhost:8080/observe/health/ready
```



## Building a Native Image

The generation of native binaries requires an installation of GraalVM 22.1.0+.

You can build a native binary using Maven as follows:

```
mvn -Pnative-image install -DskipTests
```

The generation of the executable binary may take a few minutes to complete depending on
your hardware and operating system. When completed, the executable file will be available
under the `target` directory and be named after the artifact ID you have chosen during the
project generation phase.

Make sure you have GraalVM locally installed:

```
$GRAALVM_HOME/bin/native-image --version
```

Build the native image using the native image profile:

```
mvn package -Pnative-image
```

This uses the helidon-maven-plugin to perform the native compilation using your installed copy of GraalVM. It might take a while to complete.
Once it completes start the application using the native executable (no JVM!):

```
./target/mico-epjm
```

Yep, it starts fast. You can exercise the application’s endpoints as before.


## Building the Docker Image

```
docker build -t mico-epjm .
```

## Running the Docker Image

```
docker run --rm -p 8080:8080 mico-epjm:latest
```

Exercise the application as described above.
                                

## Run the application in Kubernetes

If you don’t have access to a Kubernetes cluster, you can [install one](https://helidon.io/docs/latest/#/about/kubernetes) on your desktop.

### Verify connectivity to cluster

```
kubectl cluster-info                        # Verify which cluster
kubectl get pods                            # Verify connectivity to cluster
```

### Deploy the application to Kubernetes

```
kubectl create -f app.yaml                              # Deploy application
kubectl get pods                                        # Wait for quickstart pod to be RUNNING
kubectl get service  mico-epjm                     # Get service info
kubectl port-forward service/mico-epjm 8081:8080   # Forward service port to 8081
```

You can now exercise the application as you did before but use the port number 8081.

After you’re done, cleanup.

```
kubectl delete -f app.yaml
```


## Building a Custom Runtime Image

Build the custom runtime image using the jlink image profile:

```
mvn package -Pjlink-image
```

This uses the helidon-maven-plugin to perform the custom image generation.
After the build completes it will report some statistics about the build including the reduction in image size.

The target/mico-epjm-jri directory is a self contained custom image of your application. It contains your application,
its runtime dependencies and the JDK modules it depends on. You can start your application using the provide start script:

```
./target/mico-epjm-jri/bin/start
```

Class Data Sharing (CDS) Archive
Also included in the custom image is a Class Data Sharing (CDS) archive that improves your application’s startup
performance and in-memory footprint. You can learn more about Class Data Sharing in the JDK documentation.

The CDS archive increases your image size to get these performance optimizations. It can be of significant size (tens of MB).
The size of the CDS archive is reported at the end of the build output.

If you’d rather have a smaller image size (with a slightly increased startup time) you can skip the creation of the CDS
archive by executing your build like this:

```
mvn package -Pjlink-image -Djlink.image.addClassDataSharingArchive=false
```

For more information on available configuration options see the helidon-maven-plugin documentation.
                                
